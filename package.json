{"name": "wallpaper-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@aws-sdk/client-s3": "^3.873.0", "@prisma/client": "^6.14.0", "@tanstack/react-query": "^5.85.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.541.0", "next": "15.5.0", "prisma": "^6.14.0", "react": "19.1.0", "react-dom": "19.1.0", "redis": "^5.8.2", "sharp": "^0.34.3", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "typescript": "^5"}}