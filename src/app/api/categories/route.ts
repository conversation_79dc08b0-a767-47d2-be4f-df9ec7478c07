import { NextResponse } from 'next/server';
import { WALLPAPER_CATEGORIES } from '@/lib/storage/r2';
import { CATEGORY_LABELS } from '@/types';

export async function GET() {
  try {
    const categories = WALLPAPER_CATEGORIES.map(category => ({
      id: category,
      name: CATEGORY_LABELS[category],
      slug: category
    }));
    
    return NextResponse.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}
