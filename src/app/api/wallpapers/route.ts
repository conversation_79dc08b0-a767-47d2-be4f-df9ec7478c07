import { NextRequest, NextResponse } from 'next/server';
import { listWallpapers } from '@/lib/storage/r2';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category') || undefined;
    const limit = parseInt(searchParams.get('limit') || '50');
    
    const wallpapers = await listWallpapers(category, limit);
    
    return NextResponse.json({
      success: true,
      data: wallpapers,
      total: wallpapers.length
    });
  } catch (error) {
    console.error('Error fetching wallpapers:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch wallpapers' },
      { status: 500 }
    );
  }
}
