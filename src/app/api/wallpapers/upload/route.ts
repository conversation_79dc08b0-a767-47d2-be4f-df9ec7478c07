import { NextRequest, NextResponse } from 'next/server';
import { uploadWallpaper } from '@/lib/storage/r2';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const category = formData.get('category') as string || 'general';
    const tags = formData.get('tags') as string;
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }
    
    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    // Parse tags
    const parsedTags = tags ? tags.split(',').map(tag => tag.trim()) : [];
    
    const result = await uploadWallpaper({
      file: buffer,
      fileName: file.name,
      contentType: file.type,
      category,
      tags: parsedTags
    });
    
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error uploading wallpaper:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to upload wallpaper' },
      { status: 500 }
    );
  }
}
