'use client';

import { useState, useEffect } from 'react';
import { use } from 'react';
import Link from 'next/link';
import { ArrowLeft, Download, Heart, Filter } from 'lucide-react';
import { WallpaperGrid } from '@/components/wallpaper/WallpaperGrid';
import { Wallpaper, WallpaperCategory, CATEGORY_LABELS } from '@/types';
import { cn } from '@/lib/utils';

// Sample data for the category
const generateSampleWallpapers = (category: WallpaperCategory, count: number = 20): Wallpaper[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: `${category}-${i + 1}`,
    fileName: `${category}-wallpaper-${i + 1}.jpg`,
    category,
    tags: [category, 'high-quality', 'mobile'],
    uploadDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    fileSize: Math.floor(Math.random() * 3000000) + 1000000,
    contentType: 'image/jpeg',
    publicUrl: `https://picsum.photos/400/600?random=${category}-${i + 1}`,
    dimensions: { 
      width: Math.random() > 0.5 ? 1920 : 1080, 
      height: Math.random() > 0.5 ? 1080 : 1920 
    },
    downloads: Math.floor(Math.random() * 5000),
    likes: Math.floor(Math.random() * 1000)
  }));
};

interface CategoryPageProps {
  params: Promise<{ category: string }>;
}

export default function CategoryPage({ params }: CategoryPageProps) {
  const { category } = use(params);
  const [wallpapers, setWallpapers] = useState<Wallpaper[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState<'latest' | 'popular' | 'downloads'>('latest');

  useEffect(() => {
    // Simulate loading wallpapers for this category
    setTimeout(() => {
      const sampleWallpapers = generateSampleWallpapers(category as WallpaperCategory);
      setWallpapers(sampleWallpapers);
      setLoading(false);
    }, 1000);
  }, [category]);

  const sortedWallpapers = [...wallpapers].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return (b.likes || 0) - (a.likes || 0);
      case 'downloads':
        return (b.downloads || 0) - (a.downloads || 0);
      case 'latest':
      default:
        return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime();
    }
  });

  const categoryLabel = CATEGORY_LABELS[category as WallpaperCategory] || 'Unknown';

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
            <Link href="/" className="hover:text-accent transition-colors">
              Home
            </Link>
            <span>/</span>
            <Link href="/categories" className="hover:text-accent transition-colors">
              Categories
            </Link>
            <span>/</span>
            <span className="text-foreground">{categoryLabel}</span>
          </div>

          {/* Back Button */}
          <Link
            href="/categories"
            className="inline-flex items-center gap-2 text-accent hover:text-accent/80 font-medium transition-colors mb-6"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Categories
          </Link>

          {/* Title */}
          <div className="space-y-4">
            <h1 className="text-3xl sm:text-4xl font-bold">
              {categoryLabel} Wallpapers
            </h1>
            <p className="text-lg text-muted-foreground">
              Discover beautiful {categoryLabel.toLowerCase()} wallpapers for all your devices.
            </p>
          </div>
        </div>

        {/* Stats and Controls */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mb-8 p-4 bg-muted/30 rounded-lg">
          <div className="flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-accent/10 flex items-center justify-center">
                <Download className="w-4 h-4 text-accent" />
              </div>
              <span className="text-muted-foreground">
                {wallpapers.reduce((sum, w) => sum + (w.downloads || 0), 0).toLocaleString()} downloads
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-red-500/10 flex items-center justify-center">
                <Heart className="w-4 h-4 text-red-500" />
              </div>
              <span className="text-muted-foreground">
                {wallpapers.reduce((sum, w) => sum + (w.likes || 0), 0).toLocaleString()} likes
              </span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 text-sm bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent"
            >
              <option value="latest">Latest</option>
              <option value="popular">Most Popular</option>
              <option value="downloads">Most Downloaded</option>
            </select>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-muted-foreground">
            {loading ? 'Loading...' : `${wallpapers.length} wallpapers found`}
          </p>
        </div>

        {/* Wallpapers Grid */}
        <WallpaperGrid
          wallpapers={sortedWallpapers}
          loading={loading}
          hasMore={false}
        />

        {/* Load More Section */}
        {!loading && wallpapers.length > 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground mb-4">
              Want to see more {categoryLabel.toLowerCase()} wallpapers?
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/wallpapers"
                className="px-6 py-3 bg-accent text-accent-foreground rounded-lg hover:bg-accent/90 transition-colors font-medium"
              >
                Browse All Wallpapers
              </Link>
              <Link
                href="/categories"
                className="px-6 py-3 border border-border rounded-lg hover:bg-accent/5 transition-colors font-medium"
              >
                Explore Other Categories
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
