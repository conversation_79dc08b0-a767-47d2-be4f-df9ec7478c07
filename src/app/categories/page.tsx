'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, ArrowRight, Grid, Image as ImageIcon } from 'lucide-react';
import { WallpaperCategory, CATEGORY_LABELS } from '@/types';
import { cn } from '@/lib/utils';

const CATEGORY_DESCRIPTIONS: Record<WallpaperCategory, string> = {
  nature: 'Stunning landscapes, forests, mountains, and natural beauty',
  abstract: 'Creative designs, patterns, and artistic expressions',
  space: 'Cosmic views, galaxies, planets, and astronomical wonders',
  animals: 'Wildlife photography and adorable animal portraits',
  architecture: 'Buildings, structures, and architectural marvels',
  automotive: 'Cars, motorcycles, and transportation designs',
  art: 'Digital art, paintings, and creative masterpieces',
  technology: 'Tech gadgets, circuits, and futuristic designs',
  minimalist: 'Clean, simple, and elegant designs',
  dark: 'Dark themes perfect for night mode and OLED displays',
  colorful: 'Vibrant, bright, and colorful compositions',
  gradient: 'Beautiful color transitions and gradient effects',
  texture: 'Patterns, textures, and material surfaces',
  landscape: 'Scenic views, horizons, and outdoor photography',
  cityscape: 'Urban views, skylines, and city life',
  general: 'Miscellaneous wallpapers for every taste'
};

const CATEGORY_EMOJIS: Record<WallpaperCategory, string> = {
  nature: '🌿',
  abstract: '🎨',
  space: '🌌',
  animals: '🐾',
  architecture: '🏛️',
  automotive: '🚗',
  art: '🖼️',
  technology: '💻',
  minimalist: '⚡',
  dark: '🌙',
  colorful: '🌈',
  gradient: '🎭',
  texture: '🧱',
  landscape: '🏔️',
  cityscape: '🏙️',
  general: '📱'
};

export default function CategoriesPage() {
  const [searchQuery, setSearchQuery] = useState('');

  const categories = Object.keys(CATEGORY_LABELS) as WallpaperCategory[];
  
  const filteredCategories = categories.filter(category =>
    CATEGORY_LABELS[category].toLowerCase().includes(searchQuery.toLowerCase()) ||
    CATEGORY_DESCRIPTIONS[category].toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
            Browse Categories
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
            Explore our collection of wallpapers organized by category. 
            Find the perfect wallpaper for your style and device.
          </p>

          {/* Search */}
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search categories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-3 text-base bg-background border-2 border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent transition-all"
            />
          </div>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredCategories.map((category) => (
            <Link
              key={category}
              href={`/categories/${category}`}
              className="group relative overflow-hidden rounded-xl bg-card border border-border hover:border-accent/50 hover:shadow-lg transition-all duration-300"
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-gradient-to-br from-accent/5 to-accent/10 group-hover:from-accent/10 group-hover:to-accent/20 transition-all duration-300" />
              
              {/* Content */}
              <div className="relative p-6 space-y-4">
                {/* Icon */}
                <div className="flex items-center justify-between">
                  <div className="w-12 h-12 rounded-xl bg-accent/10 group-hover:bg-accent/20 flex items-center justify-center text-2xl transition-colors">
                    {CATEGORY_EMOJIS[category]}
                  </div>
                  <ArrowRight className="w-5 h-5 text-muted-foreground group-hover:text-accent transition-colors" />
                </div>

                {/* Title */}
                <h3 className="text-xl font-semibold group-hover:text-accent transition-colors">
                  {CATEGORY_LABELS[category]}
                </h3>

                {/* Description */}
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {CATEGORY_DESCRIPTIONS[category]}
                </p>

                {/* Stats */}
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Grid className="w-3 h-3" />
                    <span>{Math.floor(Math.random() * 500) + 100} wallpapers</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <ImageIcon className="w-3 h-3" />
                    <span>HD & 4K</span>
                  </div>
                </div>

                {/* Preview Dots */}
                <div className="flex items-center gap-1 pt-2">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div
                      key={i}
                      className={cn(
                        "w-2 h-2 rounded-full transition-all duration-300",
                        i === 0 
                          ? "bg-accent group-hover:scale-125" 
                          : "bg-muted-foreground/30 group-hover:bg-accent/50"
                      )}
                    />
                  ))}
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* No Results */}
        {filteredCategories.length === 0 && (
          <div className="text-center py-16">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
              <Search className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">No categories found</h3>
            <p className="text-muted-foreground mb-4">
              Try adjusting your search term or browse all categories.
            </p>
            <button
              onClick={() => setSearchQuery('')}
              className="px-4 py-2 bg-accent text-accent-foreground rounded-lg hover:bg-accent/90 transition-colors"
            >
              Clear Search
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
