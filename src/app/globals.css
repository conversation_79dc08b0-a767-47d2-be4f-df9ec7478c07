@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --accent: #3b82f6;
  --accent-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #f1f5f9;
  --ring: #3b82f6;
  --radius: 0.5rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: var(--radius);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #fafafa;
    --accent: #3b82f6;
    --accent-foreground: #ffffff;
    --border: #27272a;
    --input: #27272a;
    --ring: #3b82f6;
  }
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Mobile-first responsive images */
img {
  max-width: 100%;
  height: auto;
}

/* Touch targets for mobile */
button, a, [role="button"] {
  min-height: 44px;
  min-width: 44px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}
