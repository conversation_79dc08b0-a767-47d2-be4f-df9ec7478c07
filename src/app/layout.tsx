import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "WallpaperHub - Beautiful Mobile-First Wallpapers",
  description: "Discover and download high-quality wallpapers optimized for mobile devices. Browse thousands of stunning wallpapers across multiple categories.",
  keywords: ["wallpapers", "mobile wallpapers", "backgrounds", "high quality", "free download"],
  authors: [{ name: "WallpaperHub Team" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
  openGraph: {
    title: "WallpaperHub - Beautiful Mobile-First Wallpapers",
    description: "Discover and download high-quality wallpapers optimized for mobile devices.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "WallpaperHub - Beautiful Mobile-First Wallpapers",
    description: "Discover and download high-quality wallpapers optimized for mobile devices.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <Header />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
