'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Search, TrendingUp, Download, Heart, ArrowRight, Sparkles } from 'lucide-react';
import { WallpaperGrid } from '@/components/wallpaper/WallpaperGrid';
import { Wallpaper, WallpaperCategory, CATEGORY_LABELS } from '@/types';
import { cn } from '@/lib/utils';

// Sample data - In a real app, this would come from your R2 bucket
const SAMPLE_WALLPAPERS: Wallpaper[] = [
  {
    id: '1',
    fileName: 'nature-sunset.jpg',
    category: 'nature',
    tags: ['sunset', 'landscape', 'golden hour'],
    uploadDate: new Date().toISOString(),
    fileSize: 2048000,
    contentType: 'image/jpeg',
    publicUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=600&fit=crop',
    dimensions: { width: 1920, height: 1080 },
    downloads: 1250,
    likes: 89
  },
  {
    id: '2',
    fileName: 'abstract-waves.jpg',
    category: 'abstract',
    tags: ['waves', 'fluid', 'colorful'],
    uploadDate: new Date().toISOString(),
    fileSize: 1536000,
    contentType: 'image/jpeg',
    publicUrl: 'https://images.unsplash.com/photo-1558591710-4b4a1ae0f04d?w=400&h=600&fit=crop',
    dimensions: { width: 1080, height: 1920 },
    downloads: 892,
    likes: 156
  },
  {
    id: '3',
    fileName: 'space-galaxy.jpg',
    category: 'space',
    tags: ['galaxy', 'stars', 'nebula'],
    uploadDate: new Date().toISOString(),
    fileSize: 3072000,
    contentType: 'image/jpeg',
    publicUrl: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=600&fit=crop',
    dimensions: { width: 2560, height: 1440 },
    downloads: 2156,
    likes: 287
  },
  {
    id: '4',
    fileName: 'minimalist-mountain.jpg',
    category: 'minimalist',
    tags: ['mountain', 'minimal', 'clean'],
    uploadDate: new Date().toISOString(),
    fileSize: 1024000,
    contentType: 'image/jpeg',
    publicUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=600&fit=crop',
    dimensions: { width: 1920, height: 1080 },
    downloads: 654,
    likes: 92
  }
];

const FEATURED_CATEGORIES: WallpaperCategory[] = ['nature', 'abstract', 'space', 'minimalist', 'dark', 'colorful'];

export default function Home() {
  const [featuredWallpapers, setFeaturedWallpapers] = useState<Wallpaper[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading wallpapers
    setTimeout(() => {
      setFeaturedWallpapers(SAMPLE_WALLPAPERS);
      setLoading(false);
    }, 1000);
  }, []);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-accent/10 via-background to-background">
        <div className="container mx-auto px-4 py-16 sm:py-24">
          <div className="max-w-3xl mx-auto text-center">
            <div className="flex items-center justify-center mb-6">
              <Sparkles className="w-8 h-8 text-accent mr-2" />
              <span className="text-sm font-medium text-accent uppercase tracking-wider">
                Mobile-First Wallpapers
              </span>
            </div>
            
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              Beautiful Wallpapers for Every Device
            </h1>
            
            <p className="text-lg sm:text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Discover thousands of high-quality wallpapers optimized for mobile, tablet, and desktop. 
              Download instantly and personalize your devices.
            </p>

            {/* Search Bar */}
            <div className="relative max-w-md mx-auto mb-8">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search wallpapers..."
                className="w-full pl-12 pr-4 py-4 text-base bg-background border-2 border-border rounded-full focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent transition-all"
              />
            </div>

            {/* Quick Actions */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/wallpapers"
                className="w-full sm:w-auto px-8 py-4 bg-accent text-accent-foreground rounded-full font-medium hover:bg-accent/90 transition-colors flex items-center justify-center gap-2"
              >
                Browse All Wallpapers
                <ArrowRight className="w-4 h-4" />
              </Link>
              
              <Link
                href="/categories"
                className="w-full sm:w-auto px-8 py-4 border-2 border-border rounded-full font-medium hover:bg-accent/5 transition-colors flex items-center justify-center gap-2"
              >
                <TrendingUp className="w-4 h-4" />
                Trending Categories
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-16 sm:py-24">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold mb-4">
              Popular Categories
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Explore our most popular wallpaper categories, curated for every style and preference.
            </p>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
            {FEATURED_CATEGORIES.map((category) => (
              <Link
                key={category}
                href={`/categories/${category}`}
                className="group relative overflow-hidden rounded-xl aspect-square bg-gradient-to-br from-accent/10 to-accent/5 hover:from-accent/20 hover:to-accent/10 transition-all duration-300 p-6 flex flex-col justify-end"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                <div className="relative z-10">
                  <h3 className="font-semibold text-foreground group-hover:text-white transition-colors">
                    {CATEGORY_LABELS[category]}
                  </h3>
                  <p className="text-sm text-muted-foreground group-hover:text-white/80 transition-colors">
                    {Math.floor(Math.random() * 500) + 100}+ wallpapers
                  </p>
                </div>
              </Link>
            ))}
          </div>

          <div className="text-center">
            <Link
              href="/categories"
              className="inline-flex items-center gap-2 text-accent hover:text-accent/80 font-medium transition-colors"
            >
              View All Categories
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Wallpapers */}
      <section className="py-16 sm:py-24 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-3xl sm:text-4xl font-bold mb-4">
                Featured Wallpapers
              </h2>
              <p className="text-lg text-muted-foreground">
                Hand-picked wallpapers trending this week.
              </p>
            </div>
            
            <Link
              href="/wallpapers"
              className="hidden sm:flex items-center gap-2 text-accent hover:text-accent/80 font-medium transition-colors"
            >
              View All
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>

          <WallpaperGrid
            wallpapers={featuredWallpapers}
            loading={loading}
            className="mb-8"
          />

          <div className="text-center sm:hidden">
            <Link
              href="/wallpapers"
              className="inline-flex items-center gap-2 px-6 py-3 bg-accent text-accent-foreground rounded-full font-medium hover:bg-accent/90 transition-colors"
            >
              View All Wallpapers
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 sm:py-24">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div className="space-y-2">
              <div className="text-3xl sm:text-4xl font-bold text-accent">10K+</div>
              <div className="text-muted-foreground">Wallpapers</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl sm:text-4xl font-bold text-accent">50K+</div>
              <div className="text-muted-foreground">Downloads</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl sm:text-4xl font-bold text-accent">15+</div>
              <div className="text-muted-foreground">Categories</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl sm:text-4xl font-bold text-accent">5K+</div>
              <div className="text-muted-foreground">Happy Users</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
