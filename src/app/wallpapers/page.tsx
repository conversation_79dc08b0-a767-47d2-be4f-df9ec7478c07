'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, Grid, List, SlidersHorizontal } from 'lucide-react';
import { WallpaperGrid } from '@/components/wallpaper/WallpaperGrid';
import { Wallpaper, WallpaperCategory, CATEGORY_LABELS } from '@/types';
import { cn } from '@/lib/utils';

// Sample data - replace with actual API calls
const SAMPLE_WALLPAPERS: Wallpaper[] = Array.from({ length: 20 }, (_, i) => ({
  id: `wallpaper-${i + 1}`,
  fileName: `wallpaper-${i + 1}.jpg`,
  category: ['nature', 'abstract', 'space', 'minimalist', 'dark'][i % 5] as WallpaperCategory,
  tags: ['tag1', 'tag2', 'tag3'],
  uploadDate: new Date().toISOString(),
  fileSize: Math.floor(Math.random() * 3000000) + 1000000,
  contentType: 'image/jpeg',
  publicUrl: `https://picsum.photos/400/600?random=${i + 1}`,
  dimensions: { width: 1920, height: 1080 },
  downloads: Math.floor(Math.random() * 2000),
  likes: Math.floor(Math.random() * 500)
}));

export default function WallpapersPage() {
  const [wallpapers, setWallpapers] = useState<Wallpaper[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'latest' | 'popular' | 'downloads'>('latest');
  const [showFilters, setShowFilters] = useState(false);

  const categories = ['all', ...Object.keys(CATEGORY_LABELS)] as const;

  useEffect(() => {
    // Simulate loading wallpapers
    setTimeout(() => {
      setWallpapers(SAMPLE_WALLPAPERS);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredWallpapers = wallpapers
    .filter(wallpaper => {
      if (selectedCategory === 'all') return true;
      return wallpaper.category === selectedCategory;
    })
    .filter(wallpaper => {
      if (!searchQuery) return true;
      return wallpaper.fileName.toLowerCase().includes(searchQuery.toLowerCase()) ||
             wallpaper.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
             wallpaper.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'popular':
          return (b.likes || 0) - (a.likes || 0);
        case 'downloads':
          return (b.downloads || 0) - (a.downloads || 0);
        case 'latest':
        default:
          return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime();
      }
    });

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl sm:text-4xl font-bold mb-4">
            Browse Wallpapers
          </h1>
          <p className="text-lg text-muted-foreground mb-6">
            Discover thousands of high-quality wallpapers for all your devices.
          </p>

          {/* Search and Filters */}
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search wallpapers, categories, or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3 text-base bg-background border-2 border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent transition-all"
              />
            </div>

            {/* Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              {/* Category Filter */}
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={cn(
                      "px-4 py-2 rounded-full text-sm font-medium transition-all",
                      selectedCategory === category
                        ? "bg-accent text-accent-foreground"
                        : "bg-muted text-muted-foreground hover:bg-accent/10 hover:text-accent"
                    )}
                  >
                    {category === 'all' ? 'All' : CATEGORY_LABELS[category as WallpaperCategory]}
                  </button>
                ))}
              </div>

              {/* Sort and View Options */}
              <div className="flex items-center gap-2">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="px-3 py-2 text-sm bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent"
                >
                  <option value="latest">Latest</option>
                  <option value="popular">Most Popular</option>
                  <option value="downloads">Most Downloaded</option>
                </select>

                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={cn(
                    "p-2 rounded-lg border border-border hover:bg-accent/10 transition-colors",
                    showFilters && "bg-accent/10 text-accent"
                  )}
                >
                  <SlidersHorizontal className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="p-4 bg-muted/50 rounded-lg border border-border">
                <h3 className="font-medium mb-4">Advanced Filters</h3>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Orientation</label>
                    <select className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent">
                      <option value="all">All</option>
                      <option value="portrait">Portrait</option>
                      <option value="landscape">Landscape</option>
                      <option value="square">Square</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Resolution</label>
                    <select className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent">
                      <option value="all">All</option>
                      <option value="hd">HD (1920×1080)</option>
                      <option value="4k">4K (3840×2160)</option>
                      <option value="mobile">Mobile (1080×1920)</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">File Size</label>
                    <select className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent">
                      <option value="all">All</option>
                      <option value="small">Small (&lt; 1MB)</option>
                      <option value="medium">Medium (1-5MB)</option>
                      <option value="large">Large (&gt; 5MB)</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Results Count */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-muted-foreground">
            {loading ? 'Loading...' : `${filteredWallpapers.length} wallpapers found`}
          </p>
        </div>

        {/* Wallpapers Grid */}
        <WallpaperGrid
          wallpapers={filteredWallpapers}
          loading={loading}
          hasMore={false}
        />

        {/* No Results */}
        {!loading && filteredWallpapers.length === 0 && (
          <div className="text-center py-16">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
              <Search className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">No wallpapers found</h3>
            <p className="text-muted-foreground mb-4">
              Try adjusting your search criteria or browse our categories.
            </p>
            <button
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
              }}
              className="px-4 py-2 bg-accent text-accent-foreground rounded-lg hover:bg-accent/90 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
