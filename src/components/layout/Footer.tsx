import Link from 'next/link';
import { Heart } from 'lucide-react';

export function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="space-y-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-accent flex items-center justify-center">
                <span className="text-accent-foreground text-sm font-bold">W</span>
              </div>
              <span className="text-lg font-bold">WallpaperHub</span>
            </Link>
            <p className="text-sm text-muted-foreground max-w-xs">
              Discover and download high-quality wallpapers for all your devices. 
              Mobile-first design for the best experience.
            </p>
          </div>

          {/* Browse */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">Browse</h4>
            <nav className="flex flex-col space-y-2">
              <Link href="/wallpapers" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                All Wallpapers
              </Link>
              <Link href="/categories" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Categories
              </Link>
              <Link href="/trending" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Trending
              </Link>
              <Link href="/latest" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Latest
              </Link>
            </nav>
          </div>

          {/* Account */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">Account</h4>
            <nav className="flex flex-col space-y-2">
              <Link href="/auth/login" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Sign In
              </Link>
              <Link href="/auth/register" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Sign Up
              </Link>
              <Link href="/favorites" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Favorites
              </Link>
              <Link href="/downloads" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Downloads
              </Link>
            </nav>
          </div>

          {/* Legal */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">Legal</h4>
            <nav className="flex flex-col space-y-2">
              <Link href="/privacy" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Terms of Service
              </Link>
              <Link href="/dmca" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                DMCA
              </Link>
              <Link href="/contact" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Contact
              </Link>
            </nav>
          </div>
        </div>

        {/* Bottom */}
        <div className="mt-8 pt-8 border-t flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
          <p className="text-xs text-muted-foreground">
            © 2024 WallpaperHub. All rights reserved.
          </p>
          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
            <span>Made with</span>
            <Heart className="h-3 w-3 text-red-500" />
            <span>for wallpaper enthusiasts</span>
          </div>
        </div>
      </div>
    </footer>
  );
}
