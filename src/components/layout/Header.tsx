'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Menu, X, Download, Heart, User } from 'lucide-react';
import { cn } from '@/lib/utils';

interface HeaderProps {
  className?: string;
}

export function Header({ className }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleSearch = () => setIsSearchOpen(!isSearchOpen);

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
      className
    )}>
      <div className="container mx-auto px-4">
        <div className="flex h-14 items-center justify-between">
          {/* Logo */}
          <Link 
            href="/" 
            className="flex items-center space-x-2 text-lg font-bold"
          >
            <div className="h-8 w-8 rounded-lg bg-accent flex items-center justify-center">
              <span className="text-accent-foreground text-sm font-bold">W</span>
            </div>
            <span className="hidden sm:inline-block">WallpaperHub</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link 
              href="/wallpapers"
              className="text-sm font-medium hover:text-accent transition-colors"
            >
              Browse
            </Link>
            <Link 
              href="/categories"
              className="text-sm font-medium hover:text-accent transition-colors"
            >
              Categories
            </Link>
            <Link 
              href="/trending"
              className="text-sm font-medium hover:text-accent transition-colors"
            >
              Trending
            </Link>
            <Link 
              href="/collections"
              className="text-sm font-medium hover:text-accent transition-colors"
            >
              Collections
            </Link>
          </nav>

          {/* Search Bar - Desktop */}
          <div className="hidden lg:flex flex-1 max-w-md mx-6">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search wallpapers..."
                className="w-full pl-10 pr-4 py-2 text-sm bg-input border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              />
            </div>
          </div>

          {/* Right Actions */}
          <div className="flex items-center space-x-2">
            {/* Search Button - Mobile */}
            <button
              onClick={toggleSearch}
              className="lg:hidden p-2 rounded-md hover:bg-accent/10 transition-colors"
              aria-label="Search"
            >
              <Search className="h-5 w-5" />
            </button>

            {/* User Actions - Desktop */}
            <div className="hidden sm:flex items-center space-x-2">
              <Link 
                href="/favorites"
                className="p-2 rounded-md hover:bg-accent/10 transition-colors"
                aria-label="Favorites"
              >
                <Heart className="h-5 w-5" />
              </Link>
              <Link 
                href="/downloads"
                className="p-2 rounded-md hover:bg-accent/10 transition-colors"
                aria-label="Downloads"
              >
                <Download className="h-5 w-5" />
              </Link>
              <Link 
                href="/profile"
                className="p-2 rounded-md hover:bg-accent/10 transition-colors"
                aria-label="Profile"
              >
                <User className="h-5 w-5" />
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={toggleMenu}
              className="md:hidden p-2 rounded-md hover:bg-accent/10 transition-colors"
              aria-label="Menu"
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Search */}
        {isSearchOpen && (
          <div className="lg:hidden py-4 border-t border-border">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search wallpapers..."
                className="w-full pl-10 pr-4 py-2 text-sm bg-input border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                autoFocus
              />
            </div>
          </div>
        )}

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <nav className="flex flex-col space-y-4">
              <Link 
                href="/wallpapers"
                className="text-sm font-medium hover:text-accent transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Browse Wallpapers
              </Link>
              <Link 
                href="/categories"
                className="text-sm font-medium hover:text-accent transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Categories
              </Link>
              <Link 
                href="/trending"
                className="text-sm font-medium hover:text-accent transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Trending
              </Link>
              <Link 
                href="/collections"
                className="text-sm font-medium hover:text-accent transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Collections
              </Link>
              
              {/* Mobile User Actions */}
              <div className="pt-4 mt-4 border-t border-border">
                <div className="flex items-center justify-around">
                  <Link 
                    href="/favorites"
                    className="flex flex-col items-center space-y-1 text-xs"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Heart className="h-5 w-5" />
                    <span>Favorites</span>
                  </Link>
                  <Link 
                    href="/downloads"
                    className="flex flex-col items-center space-y-1 text-xs"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Download className="h-5 w-5" />
                    <span>Downloads</span>
                  </Link>
                  <Link 
                    href="/profile"
                    className="flex flex-col items-center space-y-1 text-xs"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <User className="h-5 w-5" />
                    <span>Profile</span>
                  </Link>
                </div>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
