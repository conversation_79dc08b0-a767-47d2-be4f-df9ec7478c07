'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Download, Heart, Eye, Share2 } from 'lucide-react';
import { Wallpaper } from '@/types';
import { cn } from '@/lib/utils';

interface WallpaperCardProps {
  wallpaper: Wallpaper;
  onClick?: (wallpaper: Wallpaper) => void;
  className?: string;
}

export function WallpaperCard({ wallpaper, onClick, className }: WallpaperCardProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const [isLiked, setIsLiked] = useState(wallpaper.isLiked || false);

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Create download link
    const link = document.createElement('a');
    link.href = wallpaper.publicUrl;
    link.download = wallpaper.fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleLike = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsLiked(!isLiked);
    // Here you would typically make an API call to save the like
  };

  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (navigator.share) {
      navigator.share({
        title: `Beautiful ${wallpaper.category} wallpaper`,
        text: `Check out this amazing ${wallpaper.category} wallpaper!`,
        url: wallpaper.publicUrl,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(wallpaper.publicUrl);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div
      className={cn(
        "group relative bg-card rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer",
        "border border-border hover:border-accent/50",
        className
      )}
      onClick={() => onClick?.(wallpaper)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Image Container */}
      <div className="relative aspect-[9/16] overflow-hidden">
        <Image
          src={wallpaper.publicUrl}
          alt={`${wallpaper.category} wallpaper`}
          fill
          className={cn(
            "object-cover transition-all duration-500",
            isLoading ? "scale-110 blur-sm" : "scale-100 blur-0",
            "group-hover:scale-105"
          )}
          onLoad={() => setIsLoading(false)}
          sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
        />
        
        {/* Loading Skeleton */}
        {isLoading && (
          <div className="absolute inset-0 bg-gradient-to-br from-muted/50 to-muted animate-pulse" />
        )}

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Action Buttons - Desktop */}
        <div className={cn(
          "absolute top-3 right-3 flex flex-col gap-2 transition-all duration-300",
          "opacity-0 translate-x-2 group-hover:opacity-100 group-hover:translate-x-0"
        )}>
          <button
            onClick={handleLike}
            className={cn(
              "p-2 rounded-full backdrop-blur-sm transition-all duration-200",
              "hover:scale-110 active:scale-95",
              isLiked 
                ? "bg-red-500/90 text-white" 
                : "bg-white/20 text-white hover:bg-white/30"
            )}
            aria-label={isLiked ? "Unlike" : "Like"}
          >
            <Heart className={cn("w-4 h-4", isLiked && "fill-current")} />
          </button>
          
          <button
            onClick={handleShare}
            className="p-2 rounded-full bg-white/20 text-white hover:bg-white/30 backdrop-blur-sm transition-all duration-200 hover:scale-110 active:scale-95"
            aria-label="Share"
          >
            <Share2 className="w-4 h-4" />
          </button>
        </div>

        {/* Category Badge */}
        <div className="absolute top-3 left-3">
          <span className="px-2 py-1 text-xs font-medium bg-black/60 text-white rounded-full backdrop-blur-sm">
            {wallpaper.category}
          </span>
        </div>

        {/* Download Button - Mobile */}
        <div className="absolute bottom-3 right-3 sm:hidden">
          <button
            onClick={handleDownload}
            className="p-3 rounded-full bg-accent text-accent-foreground shadow-lg hover:scale-110 active:scale-95 transition-all duration-200"
            aria-label="Download"
          >
            <Download className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Card Footer */}
      <div className="p-3 space-y-2">
        {/* File Info */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span className="truncate">
            {wallpaper.dimensions && 
              `${wallpaper.dimensions.width}×${wallpaper.dimensions.height}`
            }
          </span>
          <span>{formatFileSize(wallpaper.fileSize)}</span>
        </div>

        {/* Tags */}
        {wallpaper.tags && wallpaper.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {wallpaper.tags.slice(0, 3).map((tag, index) => (
              <span 
                key={index}
                className="px-1.5 py-0.5 text-xs bg-accent/10 text-accent rounded"
              >
                {tag}
              </span>
            ))}
            {wallpaper.tags.length > 3 && (
              <span className="px-1.5 py-0.5 text-xs text-muted-foreground">
                +{wallpaper.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Actions - Desktop */}
        <div className="hidden sm:flex items-center justify-between pt-1">
          <div className="flex items-center gap-3 text-xs text-muted-foreground">
            {wallpaper.downloads && (
              <span className="flex items-center gap-1">
                <Download className="w-3 h-3" />
                {wallpaper.downloads}
              </span>
            )}
            {wallpaper.likes && (
              <span className="flex items-center gap-1">
                <Heart className="w-3 h-3" />
                {wallpaper.likes}
              </span>
            )}
          </div>
          
          <button
            onClick={handleDownload}
            className="px-3 py-1.5 text-xs font-medium bg-accent text-accent-foreground rounded-md hover:bg-accent/90 transition-colors"
          >
            Download
          </button>
        </div>

        {/* Actions - Mobile */}
        <div className="sm:hidden flex items-center justify-between pt-1">
          <div className="flex items-center gap-4">
            <button
              onClick={handleLike}
              className={cn(
                "flex items-center gap-1 text-xs transition-colors",
                isLiked ? "text-red-500" : "text-muted-foreground"
              )}
            >
              <Heart className={cn("w-3 h-3", isLiked && "fill-current")} />
              {wallpaper.likes || 0}
            </button>
            
            <button
              onClick={handleShare}
              className="flex items-center gap-1 text-xs text-muted-foreground"
            >
              <Share2 className="w-3 h-3" />
              Share
            </button>
          </div>
          
          <span className="text-xs text-muted-foreground flex items-center gap-1">
            <Download className="w-3 h-3" />
            {wallpaper.downloads || 0}
          </span>
        </div>
      </div>
    </div>
  );
}
