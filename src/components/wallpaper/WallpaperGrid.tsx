'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Heart, Download, Share2, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Wallpaper } from '@/types';

interface WallpaperGridProps {
  wallpapers: Wallpaper[];
  loading?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
  className?: string;
}

interface WallpaperCardProps {
  wallpaper: Wallpaper;
  onFavorite?: (id: string) => void;
  onDownload?: (id: string) => void;
}

function WallpaperCard({ wallpaper, onFavorite, onDownload }: WallpaperCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);

  const handleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorited(!isFavorited);
    onFavorite?.(wallpaper.id);
  };

  const handleDownload = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onDownload?.(wallpaper.id);
  };

  const handleShare = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${wallpaper.category} wallpaper`,
          url: wallpaper.publicUrl,
        });
      } catch (error) {
        console.log('Share cancelled');
      }
    }
  };

  return (
    <Link href={`/wallpaper/${wallpaper.id}`}>
      <div 
        className={cn(
          "group relative overflow-hidden rounded-lg bg-muted aspect-[3/4] cursor-pointer transition-all duration-300",
          "hover:scale-[1.02] hover:shadow-lg",
          imageLoaded && "bg-transparent"
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Image */}
        <Image
          src={wallpaper.publicUrl}
          alt={`${wallpaper.category} wallpaper`}
          fill
          className={cn(
            "object-cover transition-all duration-500",
            imageLoaded ? "opacity-100" : "opacity-0",
            "group-hover:scale-105"
          )}
          sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
          onLoad={() => setImageLoaded(true)}
          loading="lazy"
        />

        {/* Loading Skeleton */}
        {!imageLoaded && (
          <div className="absolute inset-0 bg-muted animate-pulse" />
        )}

        {/* Overlay - Mobile: Always visible, Desktop: Hover */}
        <div className={cn(
          "absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent",
          "opacity-100 md:opacity-0 md:group-hover:opacity-100 transition-opacity duration-300"
        )}>
          {/* Action Buttons - Top Right */}
          <div className="absolute top-2 right-2 flex flex-col space-y-2">
            <button
              onClick={handleFavorite}
              className={cn(
                "p-2 rounded-full backdrop-blur-sm transition-all duration-200 touch-manipulation",
                isFavorited 
                  ? "bg-red-500 text-white" 
                  : "bg-black/30 text-white hover:bg-black/50"
              )}
              aria-label="Add to favorites"
            >
              <Heart className={cn("h-4 w-4", isFavorited && "fill-current")} />
            </button>
            
            <button
              onClick={handleShare}
              className="p-2 rounded-full bg-black/30 text-white hover:bg-black/50 backdrop-blur-sm transition-all duration-200 touch-manipulation"
              aria-label="Share wallpaper"
            >
              <Share2 className="h-4 w-4" />
            </button>
          </div>

          {/* Info - Bottom */}
          <div className="absolute bottom-0 left-0 right-0 p-3">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <h3 className="text-white text-sm font-medium truncate">
                  {wallpaper.fileName}
                </h3>
                <p className="text-white/80 text-xs">
                  {wallpaper.dimensions ? `${wallpaper.dimensions.width} × ${wallpaper.dimensions.height}` : 'Unknown'}
                </p>
              </div>
              
              <button
                onClick={handleDownload}
                className="ml-2 p-2 rounded-full bg-accent text-accent-foreground hover:bg-accent/90 transition-colors duration-200 touch-manipulation"
                aria-label="Download wallpaper"
              >
                <Download className="h-4 w-4" />
              </button>
            </div>

            {/* Stats */}
            <div className="flex items-center space-x-4 mt-2 text-white/80 text-xs">
              <div className="flex items-center space-x-1">
                <Download className="h-3 w-3" />
                <span>{wallpaper.downloads || 0}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Heart className="h-3 w-3" />
                <span>{wallpaper.likes || 0}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Category Badge */}
        <div className="absolute top-2 left-2">
          <span className="px-2 py-1 text-xs font-medium bg-black/30 text-white rounded-full backdrop-blur-sm">
            {wallpaper.category}
          </span>
        </div>
      </div>
    </Link>
  );
}

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {Array.from({ length: 10 }).map((_, i) => (
          <div
            key={i}
            className="aspect-[3/4] bg-muted rounded-lg animate-pulse"
          />
        ))}
      </div>
    </div>
  );
}

export function WallpaperGrid({ 
  wallpapers, 
  loading = false, 
  hasMore = false, 
  onLoadMore,
  className 
}: WallpaperGridProps) {
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Infinite scroll functionality
  useEffect(() => {
    if (!hasMore || loading || isLoadingMore || !onLoadMore) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsLoadingMore(true);
          onLoadMore();
          setTimeout(() => setIsLoadingMore(false), 1000);
        }
      },
      { threshold: 0.1 }
    );

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [hasMore, loading, isLoadingMore, onLoadMore]);

  const handleFavorite = (id: string) => {
    // TODO: Implement favorite functionality
    console.log('Favorite wallpaper:', id);
  };

  const handleDownload = (id: string) => {
    // TODO: Implement download functionality
    console.log('Download wallpaper:', id);
  };

  if (loading && wallpapers.length === 0) {
    return <LoadingSkeleton />;
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {wallpapers.map((wallpaper) => (
          <WallpaperCard
            key={wallpaper.id}
            wallpaper={wallpaper}
            onFavorite={handleFavorite}
            onDownload={handleDownload}
          />
        ))}
      </div>

      {/* Load More Trigger */}
      {hasMore && (
        <div 
          ref={loadMoreRef}
          className="flex justify-center py-8"
        >
          {isLoadingMore && (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 w-full">
              {Array.from({ length: 5 }).map((_, i) => (
                <div
                  key={i}
                  className="aspect-[3/4] bg-muted rounded-lg animate-pulse"
                />
              ))}
            </div>
          )}
        </div>
      )}

      {/* No Results */}
      {!loading && wallpapers.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
            <Eye className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">No wallpapers found</h3>
          <p className="text-muted-foreground">
            Try adjusting your search criteria or browse our categories.
          </p>
        </div>
      )}
    </div>
  );
}
