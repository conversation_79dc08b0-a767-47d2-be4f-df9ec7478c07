import { S3Client, GetObjectCommand, PutObjectCommand, ListObjectsV2Command, DeleteObjectCommand } from '@aws-sdk/client-s3';

// Initialize R2 client
const r2Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
});

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME!;
const PUBLIC_DOMAIN = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_DOMAIN!;

export interface UploadWallpaperParams {
  file: Buffer;
  fileName: string;
  contentType: string;
  category?: string;
  tags?: string[];
  metadata?: Record<string, string>;
}

export interface WallpaperMetadata {
  id: string;
  fileName: string;
  category: string;
  tags: string[];
  uploadDate: string;
  fileSize: number;
  contentType: string;
  publicUrl: string;
  dimensions?: {
    width: number;
    height: number;
  };
}

// Upload wallpaper to R2
export async function uploadWallpaper({
  file,
  fileName,
  contentType,
  category = 'general',
  tags = [],
  metadata = {}
}: UploadWallpaperParams): Promise<WallpaperMetadata> {
  const fileId = `${Date.now()}-${fileName.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
  const key = `wallpapers/${category}/${fileId}`;

  const uploadParams = {
    Bucket: BUCKET_NAME,
    Key: key,
    Body: file,
    ContentType: contentType,
    Metadata: {
      category,
      tags: tags.join(','),
      uploadDate: new Date().toISOString(),
      ...metadata
    }
  };

  try {
    await r2Client.send(new PutObjectCommand(uploadParams));
    
    const wallpaperMetadata: WallpaperMetadata = {
      id: fileId,
      fileName,
      category,
      tags,
      uploadDate: new Date().toISOString(),
      fileSize: file.length,
      contentType,
      publicUrl: `https://${PUBLIC_DOMAIN}/${key}`
    };

    return wallpaperMetadata;
  } catch (error) {
    console.error('Error uploading wallpaper:', error);
    throw new Error('Failed to upload wallpaper');
  }
}

// Get wallpaper metadata from R2
export async function getWallpaperMetadata(key: string): Promise<WallpaperMetadata | null> {
  try {
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key
    });

    const response = await r2Client.send(command);
    
    if (!response.Metadata) {
      return null;
    }

    const metadata: WallpaperMetadata = {
      id: key.split('/').pop() || '',
      fileName: response.Metadata.filename || '',
      category: response.Metadata.category || 'general',
      tags: response.Metadata.tags ? response.Metadata.tags.split(',') : [],
      uploadDate: response.Metadata.uploaddate || '',
      fileSize: response.ContentLength || 0,
      contentType: response.ContentType || '',
      publicUrl: `https://${PUBLIC_DOMAIN}/${key}`
    };

    return metadata;
  } catch (error) {
    console.error('Error getting wallpaper metadata:', error);
    return null;
  }
}

// List wallpapers from R2
export async function listWallpapers(category?: string, limit?: number): Promise<WallpaperMetadata[]> {
  try {
    const prefix = category ? `wallpapers/${category}/` : 'wallpapers/';
    
    const command = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: prefix,
      MaxKeys: limit || 50
    });

    const response = await r2Client.send(command);
    
    if (!response.Contents) {
      return [];
    }

    const wallpapers: WallpaperMetadata[] = [];

    for (const object of response.Contents) {
      if (!object.Key) continue;

      const metadata = await getWallpaperMetadata(object.Key);
      if (metadata) {
        wallpapers.push(metadata);
      }
    }

    return wallpapers.sort((a, b) => 
      new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime()
    );
  } catch (error) {
    console.error('Error listing wallpapers:', error);
    return [];
  }
}

// Delete wallpaper from R2
export async function deleteWallpaper(key: string): Promise<boolean> {
  try {
    const command = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key
    });

    await r2Client.send(command);
    return true;
  } catch (error) {
    console.error('Error deleting wallpaper:', error);
    return false;
  }
}

// Get public URL for wallpaper
export function getWallpaperUrl(key: string): string {
  return `https://${PUBLIC_DOMAIN}/${key}`;
}

// Generate wallpaper categories
export const WALLPAPER_CATEGORIES = [
  'nature',
  'abstract',
  'space',
  'animals',
  'architecture',
  'automotive',
  'art',
  'technology',
  'minimalist',
  'dark',
  'colorful',
  'gradient',
  'texture',
  'landscape',
  'cityscape'
] as const;

export type WallpaperCategory = typeof WALLPAPER_CATEGORIES[number];
