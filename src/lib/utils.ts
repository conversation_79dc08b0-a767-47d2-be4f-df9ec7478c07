import { type ClassValue, clsx } from "clsx";

// Utility function for combining class names (similar to shadcn/ui)
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

// Format file size in human readable format
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Format download count in human readable format
export function formatDownloadCount(count: number): string {
  if (count < 1000) return count.toString();
  if (count < 1000000) return (count / 1000).toFixed(1) + 'K';
  return (count / 1000000).toFixed(1) + 'M';
}

// Get aspect ratio from dimensions
export function getAspectRatio(width: number, height: number): string {
  const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b);
  const divisor = gcd(width, height);
  return `${width / divisor}:${height / divisor}`;
}

// Get orientation from dimensions
export function getOrientation(width: number, height: number): 'portrait' | 'landscape' | 'square' {
  if (width === height) return 'square';
  return width > height ? 'landscape' : 'portrait';
}

// Generate responsive image URLs for different screen sizes
export function generateImageUrls(baseUrl: string, filename: string) {
  const baseUrlWithoutExt = baseUrl.replace(/\.[^/.]+$/, "");
  
  return {
    thumbnail: `${baseUrlWithoutExt}_thumb.webp`,
    small: `${baseUrlWithoutExt}_small.webp`,
    medium: `${baseUrlWithoutExt}_medium.webp`,
    large: `${baseUrlWithoutExt}_large.webp`,
    original: baseUrl,
  };
}

// Debounce function for search
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Throttle function for scroll events
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Get responsive image sizes for different breakpoints
export function getImageSizes(isFeatured: boolean = false): string {
  if (isFeatured) {
    return "(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw";
  }
  return "(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw";
}

// Calculate grid columns based on screen width
export function getGridColumns(width: number): number {
  if (width < 640) return 2; // mobile: 2 columns
  if (width < 1024) return 3; // tablet: 3 columns
  if (width < 1536) return 4; // desktop: 4 columns
  return 5; // large desktop: 5 columns
}

// Format date for display
export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const now = new Date();
  const diffMs = now.getTime() - dateObj.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) return 'Today';
  if (diffDays === 1) return 'Yesterday';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
}

// Generate SEO-friendly slug from title
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

// Validate image file type
export function isValidImageFile(file: File): boolean {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  return validTypes.includes(file.type);
}

// Get dominant color from image (placeholder function)
export function getDominantColor(imageUrl: string): Promise<string> {
  return new Promise((resolve) => {
    // This would typically use a library like color-thief
    // For now, return a default color
    resolve('#3b82f6');
  });
}

// Copy text to clipboard
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
}

// Check if user is on mobile device
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
}

// Get device pixel ratio for high-DPI displays
export function getDevicePixelRatio(): number {
  if (typeof window === 'undefined') return 1;
  return window.devicePixelRatio || 1;
}
