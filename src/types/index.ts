export interface Wallpaper {
  id: string;
  fileName: string;
  category: string;
  tags: string[];
  uploadDate: string;
  fileSize: number;
  contentType: string;
  publicUrl: string;
  thumbnailUrl?: string;
  dimensions?: {
    width: number;
    height: number;
  };
  downloads?: number;
  likes?: number;
  isLiked?: boolean;
  isDownloaded?: boolean;
}

export interface WallpaperGridProps {
  wallpapers: Wallpaper[];
  loading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
  className?: string;
}

export interface WallpaperCardProps {
  wallpaper: Wallpaper;
  onClick?: (wallpaper: Wallpaper) => void;
  className?: string;
}

export interface CategoryFilter {
  category: string;
  count: number;
  isActive: boolean;
}

export interface SearchFilters {
  category?: string;
  tags?: string[];
  sortBy?: 'latest' | 'popular' | 'downloads';
  orientation?: 'portrait' | 'landscape' | 'square';
}

export interface DownloadOptions {
  quality: 'original' | 'high' | 'medium';
  format?: 'jpg' | 'png' | 'webp';
}

export type WallpaperCategory = 
  | 'nature'
  | 'abstract' 
  | 'space'
  | 'animals'
  | 'architecture'
  | 'automotive'
  | 'art'
  | 'technology'
  | 'minimalist'
  | 'dark'
  | 'colorful'
  | 'gradient'
  | 'texture'
  | 'landscape'
  | 'cityscape'
  | 'general';

export const CATEGORY_LABELS: Record<WallpaperCategory, string> = {
  nature: 'Nature',
  abstract: 'Abstract',
  space: 'Space',
  animals: 'Animals',
  architecture: 'Architecture',
  automotive: 'Automotive',
  art: 'Art',
  technology: 'Technology',
  minimalist: 'Minimalist',
  dark: 'Dark',
  colorful: 'Colorful',
  gradient: 'Gradient',
  texture: 'Texture',
  landscape: 'Landscape',
  cityscape: 'Cityscape',
  general: 'General'
};